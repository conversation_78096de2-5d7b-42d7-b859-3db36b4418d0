{"watermark.show.title": false, "welcome.firstLaunchKey": "qoder.firstLaunch", "commit": "bb7f40c9458d6437a38f9ea187fcd2ab0389eb1e", "date": "2025-08-30T05:15:07.538Z", "checksums": {"vs/base/parts/sandbox/electron-sandbox/preload.js": "mBid5aA8ek8lpMqmcr/qQ38IasqV0zrZ54ukPkn5puY", "vs/workbench/workbench.desktop.main.js": "832OBG5lgEl0HKL0AqLcCsFevgYjVnaav/2Opa1+WNg", "vs/workbench/workbench.desktop.main.css": "cAo/JhCNnt5Cb/v/SyO7x96HeWPaeBF9s51Qqxaxp/k", "vs/workbench/api/node/extensionHostProcess.js": "or00R2xHEYIX1X2f+BF8OZ/XRHBNT/5svPaaSSa3XAs", "vs/code/electron-sandbox/workbench/workbench.html": "umJmr/yUxJDYat/VrxUKpkphx/EVwnnMv+EFbCz2zoE", "vs/code/electron-sandbox/workbench/workbench.js": "8RyhhI+zq4T5cl/SPEl7OFSei1cZxpoxW4YiYisPTP4"}, "version": "0.1.20"}