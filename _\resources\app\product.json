{"nameShort": "<PERSON><PERSON><PERSON>", "nameLong": "<PERSON><PERSON><PERSON>", "applicationName": "qoder", "dataFolderName": ".qoder", "win32MutexName": "<PERSON><PERSON><PERSON>", "licenseName": "Proprietary", "licenseUrl": "https://qoder.com/product-service", "serverLicenseUrl": "https://qoder.com/product-service", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "qoder-server", "serverDataFolderName": ".qoder-server", "tunnelApplicationName": "qoder-tunnel", "win32DirName": "<PERSON><PERSON><PERSON>", "win32NameVersion": "<PERSON><PERSON><PERSON>", "win32RegValueName": "<PERSON><PERSON><PERSON>", "win32x64AppId": "{{1E3C56D8-2ED2-406D-ABCF-423258E2D1AB}", "win32arm64AppId": "{{7C97A78A-A305-4065-A6CF-AF6EEFFB65A3}", "win32x64UserAppId": "{{943D6004-554E-4B49-A1D5-52F999A1B3C9}", "win32arm64UserAppId": "{{B8942D22-F89D-400B-988B-D350A9C3F6EA}", "win32AppUserModelId": "AlibabaCloud.Qoder", "win32ShellNameShort": "<PERSON><PERSON><PERSON>", "win32TunnelServiceMutex": "Qoder-tunnelservice", "win32TunnelMutex": "Qoder-tunnel", "darwinBundleIdentifier": "com.qoder.ide", "darwinProfileUUID": "87F30917-6BED-4D64-840B-7C3B8157C9A4", "darwinProfilePayloadUUID": "799F6F77-8223-4528-BEC0-BAD07A763F9F", "linuxIconName": "<PERSON><PERSON><PERSON>", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "", "nodejsRepository": "https://nodejs.org", "urlProtocol": "<PERSON><PERSON><PERSON>", "webviewContentExternalBaseUrlTemplate": "https://{{uuid}}.vscode-cdn.net/insider/ef65ac1ba57f57f2a3961bfe94aa20481caca4c6/out/vs/workbench/contrib/webview/browser/pre/", "trustedExtensionProtocolHandlers": ["aicoding.aicoding-agent"], "builtInExtensions": [{"name": "ms-vscode.js-debug-companion", "version": "1.1.3", "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93", "repo": "https://github.com/microsoft/vscode-js-debug-companion", "metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.js-debug", "version": "1.100.0", "sha256": "69c746edf41508510818991e1d1a7cbac9d474dd666595540163d3cab435aeb9", "repo": "https://github.com/microsoft/vscode-js-debug", "metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.10", "sha256": "7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08", "repo": "https://github.com/microsoft/vscode-js-profile-visualizer", "metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "aicoding.aicoding-agent", "version": "0.1.20", "vsix": ".build\\builtInExtensions\\aicoding.aicoding-agent.vsix", "sha256": "6c37e22320e5de48f22fdeba34d2156c461dbc4811815ea6b1e5c96dbe3782ae", "extractPath": ".build\\builtInExtensions\\aicoding.aicoding-agent"}, {"name": "ms-vscode.js-debug-companion", "version": "1.1.3", "vsix": ".build\\builtInExtensions\\ms-vscode.js-debug-companion.vsix", "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93", "extractPath": ".build\\builtInExtensions\\ms-vscode.js-debug-companion"}, {"name": "ms-vscode.js-debug", "version": "1.100.0", "vsix": ".build\\builtInExtensions\\ms-vscode.js-debug.vsix", "sha256": "69c746edf41508510818991e1d1a7cbac9d474dd666595540163d3cab435aeb9", "extractPath": ".build\\builtInExtensions\\ms-vscode.js-debug"}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.10", "vsix": ".build\\builtInExtensions\\ms-vscode.vscode-js-profile-table.vsix", "sha256": "7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08", "extractPath": ".build\\builtInExtensions\\ms-vscode.vscode-js-profile-table"}], "releaseNotesUrl": "https://qoder.com/changelog", "globalReleaseNotesUrl": "https://qoder.com/changelog", "documentationUrl": "https://docs.qoder.com", "globalDocumentationUrl": "https://docs.qoder.com", "serverDocumentationUrl": "https://docs.qoder.com", "aiConfig": {"ariaKey": "btardsb9ml@8031b125dd8bfcf_btardsb9ml@53df7ad2afe8301"}, "globalLicenseUrl": "https://qoder.com/product-service", "extensions.verifySignature": false, "enableTelemetry": true, "extensionEnabledApiProposals": {"*": ["*"]}, "trustedExtensionPublishers": ["aicoding"], "extensionUntrustedWorkspaceSupport": {"aicoding.aicoding-agent": {"default": true}}, "linkProtectionTrustedDomains": ["*.aliyun.com", "*.qoder.ai", "*.qoder.com", "*.qoder.sh", "*.alibabacloud.com"], "extensionRecommendations": {"llvm-vs-code-extensions.vscode-clangd": {"onFileOpen": [{"pathGlob": "{**/*.c,**/*.cpp,**/*.cppm,**/*.cc,**/*.ccm,**/*.cxx,**/*.cxxm,**/*.c++,**/*.c++m,**/*.hh,**/*.hpp,**/*.hxx,**/*.h++,**/*.h}", "important": true, "whenNotInstalled": ["ms-vscode.cpptools", "ccls-project.ccls", "cquery-project.cquery"]}, {"languages": ["c", "cpp"], "important": true, "whenNotInstalled": ["ms-vscode.cpptools", "ccls-project.ccls", "cquery-project.cquery"]}]}, "golang.Go": {"onFileOpen": [{"pathGlob": "**/*.go", "important": true}, {"languages": ["go"], "important": true}]}, "detachhead.basedpyright": {"onFileOpen": [{"pathGlob": "{**/*.py}", "important": true, "whenNotInstalled": ["ms-pyright.pyright"]}, {"languages": ["python"], "important": true, "whenNotInstalled": ["ms-pyright.pyright"]}]}, "ms-python.python": {"onFileOpen": [{"pathGlob": "{**/*.py}", "important": true}, {"languages": ["python"], "important": true}, {"pathGlob": "{**/*.ipynb}"}]}, "muhammad-sammy.csharp": {"onFileOpen": [{"pathGlob": "{**/*.cs,**/*.csx,**/*.cake,**/global.json,**/*.csproj,**/*.cshtml,**/*.sln}", "important": true, "whenNotInstalled": ["ms-dotnettools.csdevkit"]}, {"languages": ["csharp"], "important": true, "whenNotInstalled": ["ms-dotnettools.csdevkit"]}]}, "Ionide.Ionide-fsharp": {"onFileOpen": [{"pathGlob": "{**/*.fs,**/*.fsx,**/*.fsi,**/*.fsscript}", "important": true}, {"languages": ["fsharp"], "important": true}]}, "vscjava.vscode-java-pack": {"onFileOpen": [{"pathGlob": "{**/*.java,**/*.jav}", "important": true, "whenNotInstalled": ["ASF.apache-netbeans-java", "Oracle.oracle-java"]}, {"languages": ["java"], "important": true, "whenNotInstalled": ["ASF.apache-netbeans-java", "Oracle.oracle-java"]}]}, "fwcd.kotlin": {"onFileOpen": [{"pathGlob": "{**/*.kt,**/*.kts}", "important": true, "whenNotInstalled": ["jetbrains.kotlin"]}, {"languages": ["kotlin"], "important": true, "whenNotInstalled": ["jetbrains.kotlin"]}]}, "bmewburn.vscode-intelephense-client": {"onFileOpen": [{"pathGlob": "{**/*.php,**/*.php4,**/*.php5,**/*.phtml,**/*.ctp,**/php.ini}", "important": true}]}, "xdebug.php-debug": {"onFileOpen": [{"pathGlob": "{**/*.php,**/*.php4,**/*.php5,**/*.phtml,**/*.ctp,**/php.ini}", "important": true}]}, "rust-lang.rust-analyzer": {"onFileOpen": [{"pathGlob": "{**/*.rs,**/*.rslib,**/Cargo.toml}", "important": true}, {"languages": ["rust"], "important": true}]}, "Shopify.ruby-lsp": {"onFileOpen": [{"pathGlob": "{**/*.rb,**/*.erb,**/*.reek,**/.fasterer.yml,**/ruby-lint.yml,**/.rubocop.yml}", "important": true}]}, "sumneko.lua": {"onFileOpen": [{"pathGlob": "{**/*.lua}", "important": true}, {"languages": ["lua"], "important": true}]}, "sas.sas-lsp": {"onFileOpen": [{"pathGlob": "{**/*.sas,**/*.sas7bdat,**/*.sas7bvew,**/*.sasxpt,**/*.lst,**/*.lstm}", "important": true}, {"languages": ["sas"], "important": true}]}, "richterger.perl": {"onFileOpen": [{"pathGlob": "{**/*.pl,**/*.pm}", "important": true}, {"languages": ["perl"], "important": true}]}, "REditorSupport.r": {"onFileOpen": [{"pathGlob": "{**/*.r}", "important": true}, {"languages": ["r"], "important": true}]}, "mechatroner.rainbow-csv": {"onFileOpen": [{"pathGlob": "**/*.csv", "important": true}]}, "tomoki1207.pdf": {"onFileOpen": [{"pathGlob": "**/*.pdf", "important": true}]}, "dart-code.dart-code": {"onFileOpen": [{"pathGlob": "{**/*.dart}", "important": true}, {"languages": ["dart"], "important": true}]}, "julialang.language-julia": {"onFileOpen": [{"pathGlob": "{**/*.jl}", "contentPattern": "^#!\\s*/.*\\bjulia[0-9.-]*\\b", "important": true}, {"pathGlob": "{**/*.jmd}", "important": true}]}, "svelte.svelte-vscode": {"onFileOpen": [{"pathGlob": "{**/*.svelte}", "important": true}]}, "ms-azuretools.vscode-docker": {"onFileOpen": [{"pathGlob": "{**/dockerfile,**/Dockerfile,**/docker-compose.yml,**/docker-compose.*.yml}", "important": true}, {"languages": ["dockerfile"], "important": true}]}, "vue.volar": {"onFileOpen": [{"pathGlob": "{**/*.vue}", "important": true}, {"languages": ["vue"], "important": true}]}}, "extensionsGallery": {"serviceUrl": "https://marketplace.qoder.sh/_apis/public/gallery", "controlUrl": "https://download.qoder.com/marketplace/controlUrl.json", "searchUrl": "https://marketplace.qoder.sh/_apis/public/gallery/extensionquery", "itemUrl": "https://marketplace.qoder.sh/items"}, "privacyStatementUrl": "https://qoder.com/privacy-policy", "keyboardShortcutsUrlMac": "https://go.microsoft.com/fwlink/?linkid=832143", "keyboardShortcutsUrlLinux": "https://go.microsoft.com/fwlink/?linkid=832144", "keyboardShortcutsUrlWin": "https://go.microsoft.com/fwlink/?linkid=832145", "globalQuestModeDocumentationUrl": "https://docs.qoder.com/user-guide/quest-mode", "productVersion": "0.1.20", "target": "user", "quality": "stable", "updateUrl": "https://center.qoder.sh/algo", "releaseType": "release", "commit": "bb7f40c9458d6437a38f9ea187fcd2ab0389eb1e", "date": "2025-08-30T05:15:07.538Z", "skipReleaseNotes": true, "checksums": {"vs/base/parts/sandbox/electron-sandbox/preload.js": "mBid5aA8ek8lpMqmcr/qQ38IasqV0zrZ54ukPkn5puY", "vs/workbench/workbench.desktop.main.js": "832OBG5lgEl0HKL0AqLcCsFevgYjVnaav/2Opa1+WNg", "vs/workbench/workbench.desktop.main.css": "cAo/JhCNnt5Cb/v/SyO7x96HeWPaeBF9s51Qqxaxp/k", "vs/workbench/api/node/extensionHostProcess.js": "or00R2xHEYIX1X2f+BF8OZ/XRHBNT/5svPaaSSa3XAs", "vs/code/electron-sandbox/workbench/workbench.html": "umJmr/yUxJDYat/VrxUKpkphx/EVwnnMv+EFbCz2zoE", "vs/code/electron-sandbox/workbench/workbench.js": "8RyhhI+zq4T5cl/SPEl7OFSei1cZxpoxW4YiYisPTP4"}, "version": "1.100.0"}